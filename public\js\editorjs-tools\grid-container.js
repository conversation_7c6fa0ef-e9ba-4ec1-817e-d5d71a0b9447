/**
 * Grid Container Tool для EditorJS
 * Создает контейнер для группировки элементов в grid-системе
 */
class GridContainer {
  static get toolbox() {
    return {
      title: 'Grid Container',
      icon: '<svg width="17" height="15" viewBox="0 0 336 276" xmlns="http://www.w3.org/2000/svg"><path d="M291 150V79c0-19-15-34-34-34H79c-19 0-34 15-34 34v42l67-44 81 72 56-29 42 30zm0 52l-43-30-56 30-81-67-67 49v62c0 18 15 34 34 34h178c17 0 31-13 34-29zM79 0h178c44 0 79 35 79 79v118c0 44-35 79-79 79H79c-44 0-79-35-79-79V79C0 35 35 0 79 0z"/></svg>',
      class: GridContainer
    };
  }

  // Проверка включенной grid-системы
  static isGridEnabled() {
    const gridToggle = document.querySelector('input[name="gridEnabled"]:checked');
    return gridToggle !== null;
  }

  static get isReadOnlySupported() {
    return true;
  }

  constructor({ data, config, api, readOnly }) {
    this.api = api;
    this.readOnly = readOnly;
    this.config = config || {};

    this.data = {
      id: data.id || this.generateId(),
      items: data.items || [],
      isActive: data.isActive || false,
      settings: data.settings || {}
    };

    this.wrapper = null;
    this.containerElement = null;

    // Проверяем grid-систему при создании
    if (!GridContainer.isGridEnabled()) {
      console.warn('Grid Container: Grid-система не включена. Включите Grid-систему в настройках блока.');
    }
  }

  generateId() {
    return 'container_' + Math.random().toString(36).substr(2, 9);
  }

  render() {
    this.wrapper = document.createElement('div');
    this.wrapper.classList.add('grid-container-tool');

    // Проверяем, включена ли grid-система
    if (!GridContainer.isGridEnabled()) {
      // Показываем предупреждение
      this.wrapper.innerHTML = `
        <div class="grid-container-warning" style="
          border: 2px dashed #f59e0b;
          border-radius: 8px;
          padding: 20px;
          text-align: center;
          background: #fef3c7;
          color: #92400e;
          margin: 10px 0;
        ">
          <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
          <h4 style="margin: 0 0 8px 0; font-weight: 600;">Grid-система не активна</h4>
          <p style="margin: 0; font-size: 14px;">
            Включите Grid-систему в настройках блока для использования контейнеров
          </p>
        </div>
      `;
      return this.wrapper;
    }

    this.containerElement = document.createElement('div');
    this.containerElement.classList.add('grid-container');
    this.containerElement.dataset.containerId = this.data.id;

    if (this.data.isActive) {
      this.containerElement.classList.add('active');
    }

    // Создаем заголовок контейнера
    const header = document.createElement('div');
    header.classList.add('grid-container-header');
    header.innerHTML = `
      <div class="grid-container-title">Grid Container</div>
      <div class="grid-container-controls">
        <button class="grid-container-tune" title="Настройки контейнера">⚙️</button>
        <button class="grid-container-delete" title="Удалить контейнер">🗑️</button>
      </div>
    `;

    // Создаем область для элементов
    const itemsArea = document.createElement('div');
    itemsArea.classList.add('grid-container-items');

    if (this.data.items.length === 0) {
      itemsArea.innerHTML = `
        <div class="grid-container-placeholder">
          <p>Кликните по контейнеру для активации, затем используйте "+" в тулбаре для добавления элементов</p>
        </div>
      `;
    } else {
      this.renderItems(itemsArea);
    }

    this.containerElement.appendChild(header);
    this.containerElement.appendChild(itemsArea);
    this.wrapper.appendChild(this.containerElement);

    // Сохраняем ссылку на инструмент в DOM элементах
    this.wrapper.__editorjs_tool = this;
    this.containerElement._gridContainerInstance = this;

    // Добавляем обработчики событий
    this.bindEvents();

    return this.wrapper;
  }

  renderItems(container) {
    container.innerHTML = '';
    this.data.items.forEach((item, index) => {
      const itemElement = document.createElement('div');
      itemElement.classList.add('grid-container-item');
      itemElement.dataset.itemId = item.id;
      itemElement.innerHTML = `
        <div class="item-preview">${this.getItemPreview(item)}</div>
        <div class="item-controls">
          <button class="move-up" ${index === 0 ? 'disabled' : ''}>↑</button>
          <button class="move-down" ${index === this.data.items.length - 1 ? 'disabled' : ''}>↓</button>
          <button class="delete-item">✕</button>
        </div>
      `;
      container.appendChild(itemElement);
    });
  }

  getItemPreview(item) {
    switch (item.type) {
      case 'paragraph':
        return `<p>${item.data?.text || 'Текст'}</p>`;
      case 'header':
        return `<h${item.data?.level || 2}>${item.data?.text || 'Заголовок'}</h${item.data?.level || 2}>`;
      case 'list':
        return `<ul><li>${item.data?.items?.[0] || 'Элемент списка'}</li></ul>`;
      case 'image':
        return `<img src="${item.data?.file?.url || ''}" alt="Изображение" style="max-width: 100px; max-height: 60px;">`;
      default:
        return `<div class="item-type">${item.type}</div>`;
    }
  }

  bindEvents() {
    // Настройки контейнера
    this.containerElement.addEventListener('click', (e) => {
      if (e.target.classList.contains('grid-container-tune')) {
        e.stopPropagation();
        this.openSettings();
      }
    });

    // Удаление контейнера
    this.containerElement.addEventListener('click', (e) => {
      if (e.target.classList.contains('grid-container-delete')) {
        e.stopPropagation();
        this.deleteContainer();
      }
    });

    // Управление элементами
    this.containerElement.addEventListener('click', (e) => {
      if (e.target.classList.contains('move-up')) {
        e.stopPropagation();
        const itemId = e.target.closest('.grid-container-item').dataset.itemId;
        this.moveItem(itemId, 'up');
      } else if (e.target.classList.contains('move-down')) {
        e.stopPropagation();
        const itemId = e.target.closest('.grid-container-item').dataset.itemId;
        this.moveItem(itemId, 'down');
      } else if (e.target.classList.contains('delete-item')) {
        e.stopPropagation();
        const itemId = e.target.closest('.grid-container-item').dataset.itemId;
        this.deleteItem(itemId);
      }
    });
  }

  // Активация контейнера
  activate() {
    console.log('🎯 Активация контейнера:', this.data.id);

    // Деактивируем другие контейнеры
    document.querySelectorAll('.grid-container.active').forEach(container => {
      if (container !== this.containerElement) {
        container.classList.remove('active');
      }
    });

    // Активируем текущий контейнер
    this.containerElement.classList.add('active');
    this.data.isActive = true;

    // Уведомляем систему об активации
    const event = new CustomEvent('gridContainerActivated', {
      detail: {
        containerId: this.data.id,
        container: this
      }
    });
    document.dispatchEvent(event);
  }

  // Деактивация контейнера
  deactivate() {
    console.log('🔄 Деактивация контейнера:', this.data.id);
    this.containerElement.classList.remove('active');
    this.data.isActive = false;
  }







  openSettings() {
    // TODO: Открыть модальное окно с настройками контейнера
    console.log('Открытие настроек контейнера', this.data.id);
  }

  deleteContainer() {
    if (confirm('Удалить контейнер и все его элементы?')) {
      // Удаляем блок из EditorJS
      const blockIndex = this.api.blocks.getCurrentBlockIndex();
      this.api.blocks.delete(blockIndex);
    }
  }

  moveItem(itemId, direction) {
    const itemIndex = this.data.items.findIndex(item => item.id === itemId);
    if (itemIndex === -1) return;

    const newIndex = direction === 'up' ? itemIndex - 1 : itemIndex + 1;
    if (newIndex < 0 || newIndex >= this.data.items.length) return;

    // Меняем местами элементы
    [this.data.items[itemIndex], this.data.items[newIndex]] =
    [this.data.items[newIndex], this.data.items[itemIndex]];

    // Перерендериваем элементы
    const itemsArea = this.containerElement.querySelector('.grid-container-items');
    this.renderItems(itemsArea);
  }

  deleteItem(itemId) {
    if (confirm('Удалить элемент из контейнера?')) {
      this.data.items = this.data.items.filter(item => item.id !== itemId);

      const itemsArea = this.containerElement.querySelector('.grid-container-items');
      if (this.data.items.length === 0) {
        itemsArea.innerHTML = `
          <div class="grid-container-placeholder">
            <p>Кликните по контейнеру для активации, затем используйте "+" в тулбаре для добавления элементов</p>
          </div>
        `;
      } else {
        this.renderItems(itemsArea);
      }
    }
  }

  addItem(itemData) {
    console.log('🎯 Grid Container addItem вызван:', {
      containerId: this.data.id,
      itemData: itemData,
      currentItems: this.data.items.length
    });

    this.data.items.push({
      id: itemData.id || this.generateId(),
      type: itemData.type,
      data: itemData.data,
      containerId: this.data.id
    });

    console.log('✅ Элемент добавлен в контейнер, всего элементов:', this.data.items.length);

    const itemsArea = this.containerElement.querySelector('.grid-container-items');
    this.renderItems(itemsArea);

    console.log('🔄 Контейнер перерендерен');
  }

  save() {
    return this.data;
  }

  static get sanitize() {
    return {
      id: false,
      items: false,
      isActive: false,
      settings: false
    };
  }
}

// Экспортируем для использования
if (typeof module !== 'undefined' && module.exports) {
  module.exports = GridContainer;
} else {
  window.GridContainer = GridContainer;
}

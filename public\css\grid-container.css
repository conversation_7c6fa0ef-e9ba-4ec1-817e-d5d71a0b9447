/**
 * Стили для Grid Container Tool
 */

.grid-container-tool {
  margin: 1rem 0;
}

.grid-container {
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  padding: 10px;
  margin: 10px;
  min-height: 80px; /* Высота примерно 2 строки */
  background: #fafafa;
  transition: all 0.3s ease;
  position: relative;
  cursor: pointer;
}

.grid-container:hover {
  border-color: #d1d5db;
  background: #f3f4f6;
}

.grid-container.active {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
  background: #f8faff;
}

.grid-container-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e5e7eb;
}

.grid-container-title {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
}

.grid-container.active .grid-container-title {
  color: #3b82f6;
}

.grid-container-controls {
  display: flex;
  gap: 8px;
}

.grid-container-controls button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  font-size: 14px;
  transition: background-color 0.2s ease;
}

.grid-container-controls button:hover {
  background-color: #f3f4f6;
}

.grid-container-tune:hover {
  background-color: #dbeafe !important;
}

.grid-container-delete:hover {
  background-color: #fee2e2 !important;
}

.grid-container-items {
  min-height: 40px;
}

.grid-container-placeholder {
  text-align: center;
  padding: 20px;
  color: #9ca3af;
  font-style: italic;
  border: 2px dashed #d1d5db;
  border-radius: 6px;
  background: #f9fafb;
}

.grid-container.active .grid-container-placeholder {
  border-color: #93c5fd;
  background: #eff6ff;
  color: #3b82f6;
}

.grid-container-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  margin: 4px 0;
  border: 1px solid #e5e7eb;
  border-radius: 4px;
  background: white;
}

.grid-container.active .grid-container-item {
  border-color: #d1d5db;
}

.item-preview {
  flex: 1;
  font-size: 13px;
  color: #374151;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.item-preview p,
.item-preview h1,
.item-preview h2,
.item-preview h3,
.item-preview h4,
.item-preview h5,
.item-preview h6 {
  margin: 0;
  font-size: inherit;
  font-weight: normal;
}

.item-preview ul {
  margin: 0;
  padding-left: 16px;
}

.item-preview img {
  max-width: 60px;
  max-height: 40px;
  object-fit: cover;
  border-radius: 2px;
}

.item-type {
  background: #f3f4f6;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  color: #6b7280;
  text-transform: uppercase;
}

.item-controls {
  display: flex;
  gap: 4px;
  margin-left: 8px;
}

.item-controls button {
  background: none;
  border: none;
  cursor: pointer;
  padding: 2px 4px;
  border-radius: 3px;
  font-size: 12px;
  color: #6b7280;
  transition: all 0.2s ease;
}

.item-controls button:hover:not(:disabled) {
  background-color: #f3f4f6;
  color: #374151;
}

.item-controls button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.item-controls .delete-item:hover {
  background-color: #fee2e2;
  color: #dc2626;
}

/* Стили для интеграции с EditorJS */
.codex-editor .grid-container-tool {
  margin: 0;
}

/* Смещение тулбара EditorJS при активном контейнере */
.grid-container.active + .ce-toolbar {
  left: calc(6% + 20px) !important;
}

/* Адаптация для мобильных устройств */
@media (max-width: 768px) {
  .grid-container {
    margin: 5px;
    padding: 8px;
  }

  .grid-container-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .grid-container-controls {
    align-self: flex-end;
  }

  .grid-container-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .item-controls {
    align-self: flex-end;
    margin-left: 0;
  }
}

/* Анимации */
.grid-container-item {
  transition: all 0.2s ease;
}

.grid-container-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Стили для режима только чтения */
.grid-container.readonly .grid-container-controls,
.grid-container.readonly .item-controls {
  display: none;
}

.grid-container.readonly {
  border-style: dashed;
  background: #f9fafb;
}

/* Стили для пустого контейнера */
.grid-container:not(.active) .grid-container-placeholder {
  opacity: 0.7;
}

/* Стили для drag and drop (для будущего использования) */
.grid-container-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

.grid-container.drop-target {
  border-color: #10b981;
  background: #ecfdf5;
}
